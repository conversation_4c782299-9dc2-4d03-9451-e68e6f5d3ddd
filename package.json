{"name": "simple-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.0.4", "@heroicons/react": "^2.0.18", "@radix-ui/react-toast": "^1.1.5", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.43.4", "@types/node": "^20.10.4", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@types/xlsx": "^0.0.35", "aos": "^3.0.0-beta.6", "class-variance-authority": "^0.7.0", "lucide-react": "0.330.0", "next": "15.3.5", "prettier-plugin-tailwindcss": "^0.6.5", "react": "19.1.0", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "tailwind-merge": "^2.3.0", "typescript": "^5.3.3", "use-debounce": "^10.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@types/aos": "^3.0.7", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6"}, "overrides": {"@types/react": "19.1.8", "@types/react-dom": "19.1.6"}}